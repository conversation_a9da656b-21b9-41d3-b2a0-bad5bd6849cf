package com.nttdata.ndvn.user.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for the User Management Self-Contained System.
 * 
 * This SCS handles all user-related functionality including authentication,
 * authorization, user lifecycle management, and role-based access control
 * within the NDVN platform architecture.
 * 
 * Key Responsibilities:
 * - User registration and profile management
 * - Authentication and session management
 * - Role and permission management
 * - Security policy enforcement
 * - User audit and compliance
 * 
 * Architecture:
 * - Domain Layer: Core business logic and entities
 * - Infrastructure Layer: Data persistence and external integrations
 * - Application Layer: Use cases and application services
 * - Events Layer: Event publishing and handling
 * - Web Layer: REST API and web configuration
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = "com.nttdata.ndvn.user")
@EnableJpaRepositories(basePackages = "com.nttdata.ndvn.user.infrastructure.repository")
@EnableTransactionManagement
public class UserManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserManagementApplication.class, args);
    }
}
